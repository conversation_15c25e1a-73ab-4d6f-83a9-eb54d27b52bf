@model User
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
    ViewData["Title"] = "الملف الشخصي";
}

<div class="container mt-4" dir="rtl">
    <!-- Profile Overview -->
    <div class="row mb-4">
        <div class="col-lg-4 mb-4 mb-lg-0">
            <!-- User Profile Card -->
            <div class="card shadow-lg border-0 rounded-4 h-100">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-user-circle me-2"></i> بيانات الملف الشخصي</h4>
                </div>
                <div class="card-body text-center">
                    <div class="user-avatar mb-4">
                        <i class="fas fa-user-circle fa-6x text-primary"></i>
                    </div>
                    <h3 class="mb-3">@Model.FullName</h3>
                    <p class="text-muted mb-2">
                        @if (User.IsInRole("Admin"))
                        {
                            <span class="badge bg-danger"><i class="fas fa-crown me-1"></i> مسؤول النظام</span>
                        }
                        else if (User.IsInRole("Manager"))
                        {
                            <span class="badge bg-warning text-dark"><i class="fas fa-user-tie me-1"></i> مدير</span>
                        }
                        else if (User.IsInRole("Supervisor"))
                        {
                            <span class="badge bg-info"><i class="fas fa-user-shield me-1"></i> مشرف</span>
                        }
                        else if (User.IsInRole("DataEntry"))
                        {
                            <span class="badge bg-secondary"><i class="fas fa-keyboard me-1"></i> مدخل بيانات</span>
                        }
                        else
                        {
                            <span class="badge bg-primary"><i class="fas fa-user me-1"></i> مستخدم</span>
                        }
                    </p>
                    <p class="text-muted">
                        <i class="fas fa-building me-2"></i>

                    </p>
                    <div class="d-grid gap-2 mt-4">
                        <a asp-action="EditProfile" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i> تعديل الملف الشخصي
                        </a>
                        <a asp-action="ChangePassword" class="btn btn-outline-secondary">
                            <i class="fas fa-key me-2"></i> تغيير كلمة المرور
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <!-- User Information Card -->
            <div class="card shadow-lg border-0 rounded-4 mb-4">
                <div class="card-header bg-dark text-white">
                    <h4 class="mb-0"><i class="fas fa-info-circle me-2"></i> المعلومات الشخصية</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-group mb-3">
                                <label class="text-muted small d-block">الاسم الكامل</label>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user text-primary me-2"></i>
                                    <span class="fs-5">@Model.FullName</span>
                                </div>
                            </div>

                            <div class="info-group mb-3">
                                <label class="text-muted small d-block">البريد الإلكتروني</label>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-envelope text-primary me-2"></i>
                                    <span class="fs-5">@Model.Email</span>
                                </div>
                            </div>
                        </div>

                        </div>
                    </div>
                </div>
            </div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize datatable for assets table with Arabic localization
            $('#userAssetsTable').DataTable({
                "pageLength": 5,
                "lengthMenu": [[5, 10, 25, -1], [5, 10, 25, "الكل"]],
                "language": {
                    "search": "<i class='fas fa-search'></i> بحث:",
                    "lengthMenu": "عرض _MENU_ أصل",
                    "info": "عرض _START_ إلى _END_ من _TOTAL_ أصل",
                    "paginate": {
                        "first": "الأول",
                        "last": "الأخير",
                        "next": "التالي",
                        "previous": "السابق"
                    },
                    "emptyTable": "لا توجد بيانات متاحة في الجدول",
                    "zeroRecords": "لم يتم العثور على نتائج مطابقة"
                },
                "ordering": true,
                "dir": "rtl"
            });

            // Animate counters
            $('.counter').each(function() {
                $(this).prop('Counter', 0).animate({
                    Counter: $(this).text()
                }, {
                    duration: 1000,
                    easing: 'swing',
                    step: function(now) {
                        $(this).text(Math.ceil(now));
                    }
                });
            });
        });
    </script>

    <style>
        /* RTL specific adjustments */
        .me-1, .me-2, .me-3, .me-4 {
            margin-right: 0 !important;
        }

        .me-1 {
            margin-left: 0.25rem !important;
        }

        .me-2 {
            margin-left: 0.5rem !important;
        }

        .me-3 {
            margin-left: 1rem !important;
        }

        .me-4 {
            margin-left: 1.5rem !important;
        }

        /* Fix datatable RTL issues */
        .dataTables_filter, .dataTables_paginate {
            text-align: left !important;
        }

        .dataTables_length, .dataTables_info {
            text-align: right !important;
        }
    </style>
}