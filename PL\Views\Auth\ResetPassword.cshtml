﻿@model ResetPasswordViewModel
@{
    ViewData["Title"] = "إعادة تعيين كلمة المرور";
    Layout = "~/Views/Shared/_AuthLayout.cshtml";
}

<div class="reset-password-container">
    <div class="card shadow-lg border-0 rounded-4">
        <div class="card-header bg-primary text-white text-center py-3 rounded-top-4">
            <h3 class="mb-0 fs-2">إعادة تعيين كلمة المرور</h3>
        </div>
        <div class="card-body p-5">
            <div class="text-center mb-4">
                <div class="lock-icon">
                    <i class="fas fa-lock fa-3x text-primary"></i>
                </div>
                <p class="text-muted fs-5 mt-3">الرجاء إدخال كلمة المرور الجديدة</p>
            </div>

            @if (!ViewData.ModelState.IsValid)
            {
                <div class="alert alert-danger alert-dismissible fade show fs-5 mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>حدث خطأ!</strong>
                    <div asp-validation-summary="All" class="mt-2"></div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            <form asp-action="ResetPassword" method="post">
                <input type="hidden" asp-for="Token" />
                <input type="hidden" asp-for="Email" />

                <div class="form-group mb-4">
                    <label asp-for="NewPassword" class="fw-bold fs-4 mb-2">كلمة المرور الجديدة</label>
                    <div class="input-group input-group-lg">
                        <input asp-for="NewPassword" class="form-control shadow-sm fs-5 p-3"
                               type="password" id="newPassword" dir="ltr">
                        <button type="button" class="btn btn-outline-secondary px-3"
                                onclick="togglePassword('newPassword')">
                            <i class="fas fa-eye fs-5" id="newPasswordIcon"></i>
                        </button>
                    </div>
                    <span asp-validation-for="NewPassword" class="text-danger fs-5 mt-2"></span>
                    <div class="password-strength-meter mt-3">
                        <div class="password-requirements fs-6">
                            <p class="text-muted"><i class="fas fa-info-circle me-2"></i>كلمة المرور يجب أن تحتوي على:</p>
                            <ul class="text-muted list-unstyled pe-3">
                                <li id="length"><i class="fas fa-circle-notch me-2"></i>8 أحرف على الأقل</li>
                                <li id="uppercase"><i class="fas fa-circle-notch me-2"></i>حرف كبير واحد على الأقل</li>
                                <li id="lowercase"><i class="fas fa-circle-notch me-2"></i>حرف صغير واحد على الأقل</li>
                                <li id="number"><i class="fas fa-circle-notch me-2"></i>رقم واحد على الأقل</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-4">
                    <label asp-for="ConfirmPassword" class="fw-bold fs-4 mb-2">تأكيد كلمة المرور</label>
                    <div class="input-group input-group-lg">
                        <input asp-for="ConfirmPassword" class="form-control shadow-sm fs-5 p-3"
                               type="password" id="confirmPassword" dir="ltr">
                        <button type="button" class="btn btn-outline-secondary px-3"
                                onclick="togglePassword('confirmPassword')">
                            <i class="fas fa-eye fs-5" id="confirmPasswordIcon"></i>
                        </button>
                    </div>
                    <span asp-validation-for="ConfirmPassword" class="text-danger fs-5 mt-2"></span>
                </div>

                <div class="d-grid gap-2 mt-5">
                    <button type="submit" class="btn btn-primary btn-lg fs-4 py-3 shadow-sm">
                        <i class="fas fa-key me-2"></i> إعادة تعيين كلمة المرور
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .reset-password-container {
        max-width: 650px;
        width: 100%;
        margin: 0 auto;
    }

    .card {
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        overflow: hidden;
        animation: fadeIn 0.8s ease-in-out;
    }

    @@keyframes fadeIn {
        from

    {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }

    }

    .lock-icon {
        background-color: rgba(13, 110, 253, 0.1);
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        animation: pulse 2s infinite;
    }

    @@keyframes pulse {
        0%

    {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.4);
    }

    70% {
        box-shadow: 0 0 0 15px rgba(13, 110, 253, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }

    }

    .form-control {
        border-radius: 8px;
        transition: all 0.3s;
    }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

    .btn-primary {
        background: linear-gradient(45deg, #2937f0, #9f1ae2);
        border: none;
        border-radius: 8px;
        transition: all 0.3s;
    }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
            background: linear-gradient(45deg, #1e2bd1, #8614c0);
        }

    /* For RTL support with input fields */
    input[dir="ltr"] {
        text-align: left;
    }

    /* Password strength indicator */
    .valid-requirement {
        color: #198754;
    }

        .valid-requirement i {
            animation: spin 0.5s ease-out;
        }

    @@keyframes spin {
        0%

    {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }

    }
</style>

<script>
    function togglePassword(fieldId) {
        var input = document.getElementById(fieldId);
        var icon = document.getElementById(fieldId + 'Icon');

        if (input.type === "password") {
            input.type = "text";
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = "password";
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // Password strength checker
    document.addEventListener('DOMContentLoaded', function() {
        const newPassword = document.getElementById('newPassword');
        const lengthCheck = document.getElementById('length');
        const uppercaseCheck = document.getElementById('uppercase');
        const lowercaseCheck = document.getElementById('lowercase');
        const numberCheck = document.getElementById('number');

        newPassword.addEventListener('input', function() {
            const password = this.value;

            // Check length
            if (password.length >= 8) {
                lengthCheck.classList.add('valid-requirement');
                lengthCheck.innerHTML = '<i class="fas fa-check-circle me-2"></i>8 أحرف على الأقل';
            } else {
                lengthCheck.classList.remove('valid-requirement');
                lengthCheck.innerHTML = '<i class="fas fa-circle-notch me-2"></i>8 أحرف على الأقل';
            }

            // Check uppercase
            if (/[A-Z]/.test(password)) {
                uppercaseCheck.classList.add('valid-requirement');
                uppercaseCheck.innerHTML = '<i class="fas fa-check-circle me-2"></i>حرف كبير واحد على الأقل';
            } else {
                uppercaseCheck.classList.remove('valid-requirement');
                uppercaseCheck.innerHTML = '<i class="fas fa-circle-notch me-2"></i>حرف كبير واحد على الأقل';
            }

            // Check lowercase
            if (/[a-z]/.test(password)) {
                lowercaseCheck.classList.add('valid-requirement');
                lowercaseCheck.innerHTML = '<i class="fas fa-check-circle me-2"></i>حرف صغير واحد على الأقل';
            } else {
                lowercaseCheck.classList.remove('valid-requirement');
                lowercaseCheck.innerHTML = '<i class="fas fa-circle-notch me-2"></i>حرف صغير واحد على الأقل';
            }

            // Check number
            if (/[0-9]/.test(password)) {
                numberCheck.classList.add('valid-requirement');
                numberCheck.innerHTML = '<i class="fas fa-check-circle me-2"></i>رقم واحد على الأقل';
            } else {
                numberCheck.classList.remove('valid-requirement');
                numberCheck.innerHTML = '<i class="fas fa-circle-notch me-2"></i>رقم واحد على الأقل';
            }
        });
    });
</script>