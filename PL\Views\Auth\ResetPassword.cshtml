﻿@model ResetPasswordViewModel
@{
    ViewData["Title"] = "إعادة تعيين كلمة المرور";
    Layout = "~/Views/Shared/_AuthLayout.cshtml";
}

<link href="~/css/Auth/ResetPassword.css" rel="stylesheet" />
<div class="reset-password-container">
    <div class="card shadow-lg border-0 rounded-4">
        <div class="card-header bg-primary text-white text-center py-3 rounded-top-4">
            <h3 class="mb-0 fs-2">إعادة تعيين كلمة المرور</h3>
        </div>
        <div class="card-body p-5">
            <div class="text-center mb-4">
                <div class="lock-icon">
                    <i class="fas fa-lock fa-3x text-primary"></i>
                </div>
                <p class="text-muted fs-5 mt-3">الرجاء إدخال كلمة المرور الجديدة</p>
            </div>

            @if (!ViewData.ModelState.IsValid)
            {
                <div class="alert alert-danger alert-dismissible fade show fs-5 mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>حدث خطأ!</strong>
                    <div asp-validation-summary="All" class="mt-2"></div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            <form asp-action="ResetPassword" method="post">
                <input type="hidden" asp-for="Token" />
                <input type="hidden" asp-for="Email" />

                <div class="form-group mb-4">
                    <label asp-for="NewPassword" class="fw-bold fs-4 mb-2">كلمة المرور الجديدة</label>
                    <div class="input-group input-group-lg">
                        <input asp-for="NewPassword" class="form-control shadow-sm fs-5 p-3"
                               type="password" id="newPassword" dir="ltr">
                        <button type="button" class="btn btn-outline-secondary px-3"
                                onclick="togglePassword('newPassword')">
                            <i class="fas fa-eye fs-5" id="newPasswordIcon"></i>
                        </button>
                    </div>
                    <span asp-validation-for="NewPassword" class="text-danger fs-5 mt-2"></span>
                    <div class="password-strength-meter mt-3">
                        <div class="password-requirements fs-6">
                            <p class="text-muted"><i class="fas fa-info-circle me-2"></i>كلمة المرور يجب أن تحتوي على:</p>
                            <ul class="text-muted list-unstyled pe-3">
                                <li id="length"><i class="fas fa-circle-notch me-2"></i>8 أحرف على الأقل</li>
                                <li id="uppercase"><i class="fas fa-circle-notch me-2"></i>حرف كبير واحد على الأقل</li>
                                <li id="lowercase"><i class="fas fa-circle-notch me-2"></i>حرف صغير واحد على الأقل</li>
                                <li id="number"><i class="fas fa-circle-notch me-2"></i>رقم واحد على الأقل</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="form-group mb-4">
                    <label asp-for="ConfirmPassword" class="fw-bold fs-4 mb-2">تأكيد كلمة المرور</label>
                    <div class="input-group input-group-lg">
                        <input asp-for="ConfirmPassword" class="form-control shadow-sm fs-5 p-3"
                               type="password" id="confirmPassword" dir="ltr">
                        <button type="button" class="btn btn-outline-secondary px-3"
                                onclick="togglePassword('confirmPassword')">
                            <i class="fas fa-eye fs-5" id="confirmPasswordIcon"></i>
                        </button>
                    </div>
                    <span asp-validation-for="ConfirmPassword" class="text-danger fs-5 mt-2"></span>
                </div>

                <div class="d-grid gap-2 mt-5">
                    <button type="submit" class="btn btn-primary btn-lg fs-4 py-3 shadow-sm">
                        <i class="fas fa-key me-2"></i> إعادة تعيين كلمة المرور
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<script src="~/js/Auth/ResetPassword.js"></script>
