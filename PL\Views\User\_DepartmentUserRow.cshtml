@model User
@{
    var userRoles = ViewBag.UserRoles as Dictionary<string, List<string>>;
    var isAdmin = User.IsInRole("Admin");
}
<tr id="<EMAIL>">
    <td>
        <div class="d-flex align-items-center">
            <div class="avatar-circle bg-primary text-white">
                @Model.FullName.Substring(0, 1).ToUpper()
            </div>
            <div class="ms-3">
                <h6 class="mb-0 fw-bold">@Model.FullName</h6>
            </div>
        </div>
    </td>
    <td dir="ltr" class="text-end">@Model.Email</td>
    <td >
        @if (userRoles != null && userRoles.ContainsKey(Model.Id))
        {
            foreach (var role in userRoles[Model.Id])
            {
                <span class="badge bg-primary me-1">@role</span>
            }
        }
        else
        {
            <span class="badge bg-secondary">لا توجد أدوار</span>
        }
    </td>

 
    <td class="text-center">
        <div class="action-buttons">
            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="عرض التفاصيل">
                <i class="fas fa-eye"></i>
            </a>
            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="تعديل المستخدم">
                <i class="fas fa-edit"></i>
            </a>
            <a asp-action="ManageRoles" asp-route-userId="@Model.Id" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="إدارة الأدوار">
                <i class="fas fa-user-tag"></i>
            </a>
            <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-sm btn-danger delete-user" data-bs-toggle="tooltip" title="حذف المستخدم">
                <i class="fas fa-trash-alt"></i>
            </a>
        </div>
    </td>
</tr> 