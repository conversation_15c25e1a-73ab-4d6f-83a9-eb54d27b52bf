﻿@{
    ViewData["Title"] = "تم إرسال رابط إعادة التعيين";
    Layout = "~/Views/Shared/_AuthLayout.cshtml";
}
<link rel="stylesheet" href="~/css/Auth/ForgotPasswordConfirmation.css">

<div class="reset-confirmation-container">
    <div class="card shadow-lg border-0 rounded-4">
        <div class="card-header bg-success text-white text-center py-3 rounded-top-4">
            <h3 class="mb-0 fs-2">تم إرسال بريد إعادة تعيين كلمة المرور</h3>
        </div>
        <div class="card-body p-5 text-center">
            <div class="success-icon mb-4">
                <i class="fas fa-check-circle fa-5x text-success"></i>
            </div>

            <div class="alert alert-success shadow-sm p-4 fs-5">
                <p class="mb-3">إذا كان هناك حساب مرتبط بالبريد الإلكتروني الذي أدخلته، فقد تم إرسال تعليمات إعادة تعيين كلمة المرور إلى ذلك العنوان.</p>
                <p class="mb-0">يرجى التحقق من بريدك الإلكتروني واتباع الرابط لإعادة تعيين كلمة المرور.</p>
            </div>

            

            <div class="mt-4">
                <a asp-action="Login" class="btn btn-primary btn-lg fs-5 py-3 px-5 shadow-sm">
                    <i class="fas fa-sign-in-alt me-2"></i> العودة لتسجيل الدخول
                </a>
            </div>
        </div>
    </div>
</div>
