﻿
@{
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Styles {
    <link href="~/css/home-dashboard.css" rel="stylesheet" />
}



<!-- إضافة الخطوط العربية -->
<link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">

<section class="content islamic-pattern">
    <div class="container-fluid">
        <h1 class="dashboard-title">لوحة التحكم</h1>

        <!-- بطاقات الإحصاءات المدمجة -->
        <div class="row g-3 mb-4">
            <!-- الأقسام -->
            <div class="col-md-4 col-sm-6">
                <div class="stats-card stats-card-warning h-100 shadow-sm">
                    <div class="card-body p-3 text-center">
                        <div class="stats-icon small-icon">
                            <i class="bi bi-diagram-3"></i>
                        </div>
                        <div class="stats-value small-value">@ViewBag.NumOfDepartments</div>
                        <div class="stats-label small-label">الأقسام</div>
                    </div>
                </div>
            </div>

            <!-- المشرفين -->
            <div class="col-md-4 col-sm-6">
                <div class="stats-card gradient-info h-100 shadow-sm">
                    <div class="card-body p-3 text-center">
                        <div class="stats-icon small-icon">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="stats-value small-value">@ViewBag.NumOfSupervisors</div>
                        <div class="stats-label small-label">المشرفين</div>
                    </div>
                </div>
            </div>

            <!-- الأصول -->
            <div class="col-md-4 col-sm-6">
                <div class="stats-card gradient-dark h-100 shadow-sm">
                    <div class="card-body p-3 text-center">
                        <div class="stats-icon small-icon">
                            <i class="bi bi-box-seam"></i>
                        </div>
                        <div class="stats-value small-value">@ViewBag.NumOfAssets</div>
                        <div class="stats-label small-label">الأصول</div>
                    </div>
                </div>
            </div>

            <!-- المنشآت -->
            <div class="col-md-4 col-sm-6">
                <div class="stats-card stats-card-primary h-100 shadow-sm">
                    <div class="card-body p-3 text-center">
                        <div class="stats-icon small-icon">
                            <i class="bi bi-building"></i>
                        </div>
                        <div class="stats-value small-value">@ViewBag.NumOfFacilities</div>
                        <div class="stats-label small-label">المنشآت</div>
                    </div>
                </div>
            </div>

            <!-- المباني -->
            <div class="col-md-4 col-sm-6">
                <div class="stats-card stats-card-success h-100 shadow-sm">
                    <div class="card-body p-3 text-center">
                        <div class="stats-icon small-icon">
                            <i class="bi bi-house"></i>
                        </div>
                        <div class="stats-value small-value">@ViewBag.NumOfBuildings</div>
                        <div class="stats-label small-label">المباني</div>
                    </div>
                </div>
            </div>

            <!-- الغرف -->
            <div class="col-md-4 col-sm-6">
                <div class="stats-card stats-card-danger h-100 shadow-sm">
                    <div class="card-body p-3 text-center">
                        <div class="stats-icon small-icon">
                            <i class="bi bi-door-closed"></i>
                        </div>
                        <div class="stats-value small-value">@ViewBag.NumOfRooms</div>
                        <div class="stats-label small-label">الغرف</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- بطاقة الإجراءات السريعة -->
        <div class="w-100">
            <div class="stat-card gradient-dark rounded-4 p-2 text-white shadow-lg">
                <div class="card-body position-relative text-center">
                    <div class="arabic-decoration"></div>
                    <h3 class="fw-bold mb-3"><i class="fas fa-bolt me-2"></i> الإجراءات السريعة</h3>
                    <p class="text-white-50 mb-4">إدارة الأصول بكفاءة من خلال هذه الاختصارات السريعة</p>
                    <div class="d-flex justify-content-center flex-wrap gap-3">
                        <a href="@Url.Action("Index", "Asset")" class="action-btn btn btn-outline-light d-flex align-items-center gap-2 px-4 py-2">
                            <i class="fas fa-box"></i> <span>إدارة الأصول</span>
                        </a>
                        <a href="@Url.Action("Index", "AssetTransfer")" class="action-btn btn btn-outline-warning d-flex align-items-center gap-2 px-4 py-2">
                            <i class="fas fa-exchange-alt"></i> <span>نقل الأصول</span>
                        </a>
                        <a href="@Url.Action("Index", "Disposal")" class="action-btn btn btn-outline-danger d-flex align-items-center gap-2 px-4 py-2">
                            <i class="fas fa-trash-alt"></i> <span>الاصول المكهنه</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>


</section>



@section Scripts {
    <script>
        // Set the URL for the DataTable AJAX call
        var getChangeLogsUrl = '@Url.Action("GetChangeLogs", "ChangeLog")';
    </script>
    <script src="~/js/home-dashboard.js"></script>
}