$(document).ready(function() {
    // Initialize datatable for assets table with Arabic localization
    $('#userAssetsTable').DataTable({
        "pageLength": 5,
        "lengthMenu": [[5, 10, 25, -1], [5, 10, 25, "الكل"]],
        "language": {
            "search": "<i class='fas fa-search'></i> بحث:",
            "lengthMenu": "عرض _MENU_ أصل",
            "info": "عرض _START_ إلى _END_ من _TOTAL_ أصل",
            "paginate": {
                "first": "الأول",
                "last": "الأخير",
                "next": "التالي",
                "previous": "السابق"
            },
            "emptyTable": "لا توجد بيانات متاحة في الجدول",
            "zeroRecords": "لم يتم العثور على نتائج مطابقة"
        },
        "ordering": true,
        "dir": "rtl"
    });

    // Animate counters
    $('.counter').each(function() {
        $(this).prop('Counter', 0).animate({
            Counter: $(this).text()
        }, {
            duration: 1000,
            easing: 'swing',
            step: function(now) {
                $(this).text(Math.ceil(now));
            }
        });
    });
});
