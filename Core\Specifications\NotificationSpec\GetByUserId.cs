﻿using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Specifications.NotificationSpec
{
    public class GetByUserId : Specification<Notification>
    {
        public GetByUserId(string id): base(n => n.UserId == id)
        {
            
        }
        public GetByUserId(string id , bool isRead): base(n => n.UserId == id && n.IsRead == isRead)
        {
            
        }
    }
}
