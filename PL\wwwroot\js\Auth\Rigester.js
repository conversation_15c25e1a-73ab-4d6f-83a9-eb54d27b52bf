﻿

    function togglePassword(fieldId) {
        const passwordField = document.getElementById(fieldId);
    const toggleIcon = document.getElementById(fieldId + 'Toggle');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
    toggleIcon.classList.remove('fa-eye');
    toggleIcon.classList.add('fa-eye-slash');
        } else {
        passwordField.type = 'password';
    toggleIcon.classList.remove('fa-eye-slash');
    toggleIcon.classList.add('fa-eye');
        }
    }

    // Show validation summary if it contains errors
    document.addEventListener('DOMContentLoaded', function() {
        const validationSummary = document.querySelector('.validation-summary-errors');
    if (validationSummary) {
            const alertContainer = validationSummary.closest('.alert');
    if (alertContainer && alertContainer.classList.contains('d-none')) {
        alertContainer.classList.remove('d-none');
            }
        }
    });
