﻿@using System.Security.Claims
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- AdminLTE RTL CSS -->
    <link rel="stylesheet" href="~/css/adminlte.rtl.css">

    <!-- Google Fonts - Arabic Support -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">
    <link href="~/css/dashbord/dashbord.css" rel="stylesheet" />
</head>
<body class="hold-transition sidebar-mini layout-fixed">

    <div class="wrapper">
        <!-- Navbar -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light px-5">
            <ul class="navbar-nav">
                <!-- Push Menu Button -->
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button">
                        <i class="fas fa-bars"></i>
                    </a>
                </li>

                <!-- Fullscreen Button -->
                <li class="nav-item">
                    <a class="nav-link" data-widget="fullscreen" href="#" role="button">
                        <i class="fas fa-expand"></i>
                    </a>
                </li>

                <!-- Notifications Icon - Only visible for Admin and Manager roles -->
          
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="notificationIcon" role="button"
                           data-bs-toggle="dropdown" data-bs-auto-close="true" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            <span class="icon-badge" id="notification-badge">0</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-start notification-dropdown" aria-labelledby="notificationIcon">
                            <div class="dropdown-header d-flex justify-content-between align-items-center">
                                <span>الإشعارات</span>
                                <div>
                                    <form asp-controller="Notification" asp-action="MarkAllAsRead" method="post" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-link text-decoration-none">تحديد الكل كمقروء</button>
                                    </form>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <div id="notification-list">
                                <div class="p-2 text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                    <p class="mb-0">جاري تحميل الإشعارات...</p>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a asp-controller="Notification" asp-action="Index" class="dropdown-item text-center">
                                عرض جميع الإشعارات
                            </a>
                        </div>
                    </li>
                

                <!-- Dark Mode Toggle -->
                <li class="nav-item">
                    <a class="nav-link" href="#" id="darkModeToggle">
                        <i class="fas fa-moon"></i>
                    </a>
                </li>
            </ul>

            <!-- Push Profile and Logout to the Left -->
            <ul class="navbar-nav ms-auto">
                <!-- ms-auto لدفع العناصر إلى أقصى اليسار -->
                <!-- Profile Dropdown -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button"
                       data-bs-toggle="dropdown" data-bs-auto-close="true" aria-expanded="false">
                        <i class="bi bi-person-circle"></i> الملف الشخصي
                    </a>
                    <div class="dropdown-menu dropdown-menu-start" aria-labelledby="navbarDropdown">
                        <h6 class="dropdown-header">خيارات المستخدم</h6>
                        <a class="dropdown-item" asp-controller="User" asp-action="Profile">
                            <i class="fas fa-user-circle ms-2"></i> عرض الملف الشخصي
                        </a>
                        <a class="dropdown-item" asp-controller="User" asp-action="EditProfile">
                            <i class="fas fa-edit ms-2"></i> تعديل الملف الشخصي
                        </a>
                        <a class="dropdown-item" asp-controller="User" asp-action="ChangePassword">
                            <i class="fas fa-key ms-2"></i> تغيير كلمة المرور
                        </a>
                        <div class="dropdown-divider"></div>
                        <form asp-controller="Auth" asp-action="Logout" method="post" class="dropdown-item">
                            @Html.AntiForgeryToken()
                            <button type="submit" class="btn btn-link text-danger p-0">
                                <i class="fas fa-sign-out-alt ms-2"></i> تسجيل الخروج
                            </button>
                        </form>
                    </div>
                </li>

            </ul>
        </nav>

        <!-- Sidebar -->
        <aside class="app-sidebar bg-dark shadow" data-bs-theme="dark">
            <!-- Brand Logo -->
            <div class="sidebar-brand" style="height: fit-content">
                <a href="#" class="brand-link">

                    <div style="text-align: center; ">
                        <img width="50px" src="~/img/star.png" />

                        <div style=" font-size: 1rem; font-weight:500">
                            نظام اداره العملاء
                        </div>
              
                    </div> <!-- Title -->
                </a>
            </div>

            <!-- User Profile Section -->
            <div class="user-panel d-flex align-items-center border-bottom border-secondary p-3">
                <div class="image me-3">
                    <i class="fas fa-user-circle fa-2x text-light"></i>
                </div>
                <div class="info">
                    <a class="d-block text-light fw-bold" asp-controller="User" asp-action="Profile">
                        @User.Identity!.Name
                    </a>
                    <small class="text-muted">@User.FindFirstValue(ClaimTypes.Role)</small>
                </div>
            </div>

            <!-- Sidebar Menu -->
            <nav class="mt-3">
                <ul class="nav nav-pills nav-sidebar flex-column" data-lte-toggle="treeview" role="menu" data-accordion="false">
                    <li class="nav-item">
                        <a href="@Url.Action("Index", "Home")" class="nav-link text-light @(ViewContext.RouteData.Values["Controller"]!.ToString() == "Home" ? "active" : "")">
                            <i class="nav-icon fas fa-tachometer-alt me-2"></i>
                            <p>لوحه التحكم</p>
                        </a>
                    </li>

                      <li class="nav-item">
                            <a href="@Url.Action("DepartmentUsers", "User")" class="nav-link text-light @(ViewContext.RouteData.Values["Controller"]!.ToString() == "User" && ViewContext.RouteData.Values["Action"]!.ToString() == "DepartmentUsers" ? "active" : "")">
                                <i class="nav-icon fas fa-users me-2"></i>
                                <p>ادارة المستخدمين</p>
                            </a>
                        </li>
                    


                </ul>
            </nav>
        </aside>
        <!-- Content Wrapper -->
        <div class="content-wrapper">
            @RenderBody()
        </div>
    </div>

    <!-- Required JS Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.10.1/browser/overlayscrollbars.browser.es6.min.js"></script>
    <script src="~/js/adminlte.js"></script>
    <script src="~/js/Notification.js"></script>

    <script>
        $(document).ready(function () {
            // Load notifications
            loadNotifications();

            // Refresh notifications every 60 seconds
            setInterval(loadNotifications, 60000);

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });

            // Initialize popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
            popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl)
            });

            // Fixed sidebar toggle function for RTL layout
            let sidebarCollapsed = false;
            $('[data-widget="pushmenu"]').on('click', function(e) {
                e.preventDefault();

                sidebarCollapsed = !sidebarCollapsed;
                $('body').toggleClass('sidebar-collapse', sidebarCollapsed);

                if (sidebarCollapsed) {
                    // Collapse sidebar - move off screen
                    $('.app-sidebar').css({
                        'right': '-250px',
                        'transition': 'right 0.3s ease'
                    });
                    $('.content-wrapper, .main-header').css({
                        'margin-right': '0',
                        'transition': 'margin-right 0.3s ease'
                    });
                } else {
                    // Expand sidebar - bring back on screen
                    $('.app-sidebar').css({
                        'right': '0',
                        'transition': 'right 0.3s ease'
                    });
                    $('.content-wrapper, .main-header').css({
                        'margin-right': '250px',
                        'transition': 'margin-right 0.3s ease'
                    });
                }
            });

            // Fullscreen toggle with cross-browser support
            $('[data-widget="fullscreen"]').on('click', function () {
                if (!document.fullscreenElement) {
                    document.documentElement.requestFullscreen().catch(err => {
                        console.warn('Error attempting to enable fullscreen:', err);
                    });
                } else if (document.exitFullscreen) {
                    document.exitFullscreen();
                }
            });

            // Dark Mode Toggle
            function applyDarkMode(enabled) {
                if (enabled) {
                    $('body').addClass('dark-mode');
                    $('#darkModeToggle i').removeClass('fa-moon').addClass('fa-sun');
                } else {
                    $('body').removeClass('dark-mode');
                    $('#darkModeToggle i').removeClass('fa-sun').addClass('fa-moon');
                }
            }

            let darkMode = localStorage.getItem('darkMode') === 'enabled';
            applyDarkMode(darkMode);

            $('#darkModeToggle').on('click', function () {
                darkMode = !darkMode;
                localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled');
                applyDarkMode(darkMode);
            });

            // Bootstrap Dropdown Initialization
            var dropdowns = [].slice.call(document.querySelectorAll('.dropdown-toggle'))
            dropdowns.map(function (dropdownToggle) {
                return new bootstrap.Dropdown(dropdownToggle, {
                    autoClose: true
                });
            });
        });
                function loadNotifications() {
            // Call the fetchUnreadNotifications function from Notification.js
            fetchUnreadNotifications();
        }
                document.addEventListener('DOMContentLoaded', function () {
            const el = document.querySelector(".main-header");
            if (el) {

                el.style.marginRight = "250px";
            }
        })

    </script>

    @RenderSection("Scripts", required: false)
</body>
</html>