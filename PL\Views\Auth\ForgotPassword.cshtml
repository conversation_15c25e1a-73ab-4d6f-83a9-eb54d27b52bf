﻿@{
    ViewData["Title"] = "نسيت كلمة المرور";
    Layout = "~/Views/Shared/_AuthLayout.cshtml";
}
<link rel="stylesheet" href="~/css/Auth/ForgotPassword.css">

<div class="forgot-password-container">
    <div class="card shadow-lg border-0 rounded-4">
        <div class="card-header bg-primary text-white text-center py-3 rounded-top-4">
            <h3 class="mb-0 fs-2">نسيت كلمة المرور</h3>
        </div>
        <div class="card-body p-5">
            <p class="text-muted text-center fs-5 mb-4">أدخل بريدك الإلكتروني لإستلام رابط إعادة تعيين كلمة المرور</p>

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show fs-5" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i> @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show fs-5" role="alert">
                    <i class="fas fa-check-circle me-2"></i> @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            <form asp-action="ForgotPassword" method="post">
                <div class="form-group mb-4">
                    <label for="email" class="fw-bold fs-4 mb-2">البريد الإلكتروني</label>
                    <div class="input-group input-group-lg">
                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                        <input type="email" class="form-control fs-5 p-3" id="email" name="email"
                               required placeholder="أدخل بريدك الإلكتروني" dir="ltr">
                    </div>
                </div>

                <div class="d-grid gap-3 mt-4">
                    <button type="submit" class="btn btn-primary btn-lg fs-4 py-3 shadow-sm">
                        <i class="fas fa-paper-plane me-2"></i> إرسال رابط إعادة التعيين
                    </button>
                    <a asp-action="Login" class="btn btn-outline-secondary btn-lg fs-5 py-2 shadow-sm">
                        <i class="fas fa-arrow-right me-2"></i> العودة لتسجيل الدخول
                    </a>
                </div>
            </form>

            <div class="text-center mt-4">
                <p class="fs-5 text-muted">
                    <i class="fas fa-info-circle me-2"></i>
                    سيتم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني في حال كان الحساب موجوداً
                </p>
            </div>
        </div>
    </div>
</div>
