
@model User
@{
    ViewData["Title"] = "إنشاء مستخدم";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- Bootstrap Icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css">

<!-- Custom Styles -->
<style>
    .card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
    }

    .btn:hover {
        transform: scale(1.05);
        transition: 0.3s ease-in-out;
    }

    .form-label {
        font-weight: bold;
    }

    .container {
        max-width: 800px;
    }

    .required::after {
        content: " *";
        color: red;
    }
</style>

<!-- Page Content -->
<div class="container mt-4">
    <div class="card shadow-sm">
        <div class="card-body">
            <h2 class="text-primary mb-4">
                <i class="bi bi-person-plus-fill me-2"></i> إنشاء مستخدم
            </h2>

            <!-- Identity error messages -->
            @{
                var allErrors = ViewData.ModelState.Values.SelectMany(v => v.Errors);
            }

            @if (allErrors.Any())
            {
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach (var error in allErrors)
                        {
                            <li>@error.ErrorMessage</li>
                        }
                    </ul>
                </div>
            }

            <form asp-action="Create" method="post">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label asp-for="FullName" class="form-label required">الاسم الكامل</label>
                        <input asp-for="FullName" class="form-control" required placeholder="Enter user's full name" />
                        <span asp-validation-for="FullName" class="text-danger"></span>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label asp-for="Email" class="form-label required">البريد الإلكتروني</label>
                        <input asp-for="Email" class="form-control" type="email" required placeholder="Enter email address" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="Password" class="form-label required">كلمة المرور</label>
                        <div class="input-group">
                            <input type="password" id="Password" name="Password" class="form-control" required placeholder="Enter password" />
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('Password')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <span class="text-danger" id="PasswordValidation"></span>
                    </div>

                    <div class="col-md-6 mb-3">
                        <label for="ConfirmPassword" class="form-label required">تأكيد كلمة المرور</label>
                        <div class="input-group">
                            <input type="password" id="ConfirmPassword" name="ConfirmPassword" class="form-control" required placeholder="Confirm password" />
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('ConfirmPassword')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <span class="text-danger" id="ConfirmPasswordValidation"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="Role" class="form-label required">الدور</label>
                        <select id="Role" name="Role" class="form-select" required>
                            <option value="">-- اختر الدور --</option>
                            <option value="Admin">Admin</option>
                            <option value="Manager">مدير </option>
                            <option value="Supervisor">مشرف</option>
                            <option value="User">مستخدم</option>
                            <option value="Data Entry">مدخل بيانات</option>
                        </select>
                        <span class="text-danger" id="RoleValidation"></span>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle"></i> إنشاء مستخدم
                    </button>
                    <a asp-action="DepartmentUsers" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> العودة إلى المستخدمين
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function togglePasswordVisibility(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const fieldType = passwordField.type;

            passwordField.type = fieldType === 'password' ? 'text' : 'password';

            // Also toggle the eye icon
            const eyeIcon = event.currentTarget.querySelector('i');
            eyeIcon.classList.toggle('bi-eye');
            eyeIcon.classList.toggle('bi-eye-slash');
        }

        // Password validation
        document.getElementById('Password').addEventListener('blur', validatePassword);
        document.getElementById('ConfirmPassword').addEventListener('blur', validateConfirmPassword);

        function validatePassword() {
            const password = document.getElementById('Password').value;
            const validation = document.getElementById('PasswordValidation');

            if (password.length < 6) {
                validation.textContent = 'يجب أن تكون كلمة المرور على الأقل 6 أحرف';
                return false;
            } else {
                validation.textContent = '';
                return true;
            }
        }

        function validateConfirmPassword() {
            const password = document.getElementById('Password').value;
            const confirmPassword = document.getElementById('ConfirmPassword').value;
            const validation = document.getElementById('ConfirmPasswordValidation');

            if (password !== confirmPassword) {
                validation.textContent = 'كلمات المرور غير متطابقة';
                return false;
            } else {
                validation.textContent = '';
                return true;
            }
        }

        // Form submission validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const isPasswordValid = validatePassword();
            const isConfirmPasswordValid = validateConfirmPassword();
            const role = document.getElementById('Role').value;

            if (!isPasswordValid || !isConfirmPasswordValid) {
                e.preventDefault();
                return;
            }

            // Validate role selection
            if (!role) {
                document.getElementById('RoleValidation').textContent = 'الرجاء اختيار دور';
                e.preventDefault();
            }
        });
    </script>
}
