﻿
.forgot-password-container {
    max-width: 600px;
    width: 100%;
    margin: 0 auto;
}

.card {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    overflow: hidden;
    animation: fadeIn 0.8s ease-in-out;
}

@@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-control {
    border-radius: 8px;
    font-size: 18px;
    transition: all 0.3s;
}

    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

.input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    font-size: 18px;
}

.btn-primary {
    background: linear-gradient(45deg, #2937f0, #9f1ae2);
    border: none;
    border-radius: 8px;
    transition: all 0.3s;
}

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
        background: linear-gradient(45deg, #1e2bd1, #8614c0);
    }

.btn-outline-secondary {
    border-radius: 8px;
    transition: all 0.3s;
}

    .btn-outline-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

/* For RTL support with input fields */
input[dir="ltr"] {
    text-align: left;
}

