﻿@using Pl.Models.NotificationViewModel
@model List<NotificationViewModel>
@{
    ViewData["Title"] = "Notifications";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container mt-4">
    <div class="card shadow-lg border-0 rounded-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h2 class="mb-0"><i class="bi bi-bell-fill me-2"></i> Notifications</h2>
            <div>
                <form asp-action="MarkAllAsRead" method="post" class="d-inline">
                    <button type="submit" class="btn btn-light">
                        <i class="bi bi-check-all"></i> Mark All as Read
                    </button>
                </form>
            </div>
        </div>
        <div class="card-body">
            @if (!Model.Any())
            {
                <div class="alert alert-info">
                    <i class="bi bi-info-circle-fill me-2"></i> You have no notifications.
                </div>
            }
            else
            {
                <div class="list-group">
                    @foreach (var notification in Model)
                    {
                        <div class="list-group-item @(notification.IsRead ? "" : "list-group-item-primary")">
                            <div class="d-flex w-100 justify-content-between align-items-center">
                                <div>
                                    <h5 class="mb-1">
                                        @if (!notification.IsRead)
                                        {
                                            <span class="badge rounded-pill bg-primary me-2">New</span>
                                        }
                                        @notification.Title
                                    </h5>
                                    <p class="mb-1">@notification.Message</p>
                                    <small class="text-muted">@notification.TimeAgo</small>
                                </div>
                                <div class="btn-group">
                                    @if (!notification.IsRead)
                                    {
                                        <form asp-action="MarkAsRead" method="post" class="d-inline">
                                            <input type="hidden" name="id" value="@notification.Id" />
                                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-check"></i>
                                            </button>
                                        </form>
                                    }
                                    @if (!string.IsNullOrEmpty(notification.ActionUrl))
                                    {
                                        <a href="@notification.ActionUrl" class="btn btn-sm btn-outline-info">
                                            <i class="bi bi-box-arrow-up-right"></i>
                                        </a>
                                    }
                                    <form asp-action="Delete" method="post" class="d-inline">
                                        <input type="hidden" name="id" value="@notification.Id" />
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>
</div>