﻿@model LoginViewModel
@{
    Layout = "~/Views/Shared/_AuthLayout.cshtml";
}
<link rel="stylesheet" href="~/css/Auth/ForgotPassword.css">

<div class="login-container">
    <div class="card shadow-lg border-0 rounded-4">
        <div class="card-body p-5">
            <h2 class="text-center fw-bold mb-4 text-primary display-5">تسجيل الدخول</h2>

            @if (TempData["RegisterMessage"] != null)
            {
                <div class="alert alert-info text-center">
                    @TempData["RegisterMessage"]
                </div>
                

            }

            @if (ViewData.ModelState.ErrorCount > 0)
            {
                <div asp-validation-summary="All" class="alert alert-danger text-center"></div>
            }
            <form asp-action="Login" method="post">
                <!-- Validation Summary -->
             

                <div class="mb-4">
                    <label asp-for="Email" class="form-label fw-semibold fs-4">البريد الإلكتروني</label>
                    <div class="input-group input-group-lg">
                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                        <input asp-for="Email" class="form-control fs-5" placeholder="أدخل بريدك الإلكتروني" dir="ltr" />
                    </div>
                    <span asp-validation-for="Email" class="text-danger fs-5 mt-2"></span>
                </div>

                <div class="mb-4">
                    <label asp-for="Password" class="form-label fw-semibold fs-4">كلمة المرور</label>
                    <div class="input-group input-group-lg">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input asp-for="Password" type="password" class="form-control fs-5" id="password" placeholder="أدخل كلمة المرور" />
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility()">
                            <i class="fas fa-eye fs-5" id="password-toggle-icon"></i>
                        </button>
                    </div>
                    <span asp-validation-for="Password" class="text-danger fs-5 mt-2"></span>
                </div>

                <div class="mb-4 d-flex justify-content-center align-items-center">
                    <div>
                        <a asp-action="ForgotPassword" class="text-primary fs-5">نسيت كلمة المرور؟</a>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary w-100 py-3 fw-bold mb-3 fs-4">
                    <i class="fas fa-sign-in-alt me-2"></i> تسجيل الدخول
                </button>

                <div class="text-center mt-4 ">
                    <p class="mb-0 fs-5">
                        ليس لديك حساب؟
                        <a asp-action="Register" class="text-decoration-none fw-semibold text-primary fs-5">إنشاء حساب جديد</a>
                    </p>
                </div>
            </form>
        </div>
    </div>
</div>


<script src="~/js/Auth/Login.js"></script>
