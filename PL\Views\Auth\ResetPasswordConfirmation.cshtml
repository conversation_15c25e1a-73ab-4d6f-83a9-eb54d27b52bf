﻿@{
    ViewData["Title"] = "تم إعادة تعيين كلمة المرور بنجاح";
    Layout = "~/Views/Shared/_AuthLayout.cshtml";
}

<div class="success-container">
    <div class="card shadow-lg border-0 rounded-4">
        <div class="card-header bg-success text-white text-center py-3 rounded-top-4">
            <h3 class="mb-0 fs-2">تم إعادة تعيين كلمة المرور بنجاح</h3>
        </div>
        <div class="card-body p-5 text-center">
            <div class="success-animation mb-4">
                <div class="checkmark-circle">
                    <div class="checkmark draw"></div>
                </div>
            </div>

            <div class="alert alert-success shadow-sm p-4 fs-5">
                <p class="mb-3">تم إعادة تعيين كلمة المرور الخاصة بك بنجاح.</p>
                <p class="mb-0">يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.</p>
            </div>

            <div class="mt-5">
                <a asp-action="Login" class="btn btn-primary btn-lg fs-5 py-3 px-5 shadow-sm">
                    <i class="fas fa-sign-in-alt me-2"></i> العودة لتسجيل الدخول
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    .success-container {
        max-width: 650px;
        width: 100%;
        margin: 0 auto;
    }

    .card {
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        overflow: hidden;
        animation: fadeInUp 0.8s ease-out;
    }

    @@keyframes fadeInUp {
        from

    {
        opacity: 0;
        transform: translateY(40px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }

    }

    .alert-success {
        background-color: rgba(25, 135, 84, 0.1);
        border-color: rgba(25, 135, 84, 0.2);
        color: #0f5132;
        border-radius: 12px;
    }

    .btn-primary {
        background: linear-gradient(45deg, #2937f0, #9f1ae2);
        border: none;
        border-radius: 8px;
        transition: all 0.3s;
    }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
            background: linear-gradient(45deg, #1e2bd1, #8614c0);
        }

    /* Checkmark Animation */
    .success-animation {
        width: 100px;
        height: 100px;
        margin: 0 auto;
    }

    .checkmark-circle {
        width: 100px;
        height: 100px;
        position: relative;
        display: inline-block;
        vertical-align: top;
        background-color: #19875445;
        border-radius: 50%;
    }

    .checkmark {
        border-radius: 5px;
    }

        .checkmark.draw:after {
            animation-delay: 100ms;
            animation-duration: 1s;
            animation-timing-function: ease;
            animation-name: checkmark;
            transform: scaleX(-1) rotate(135deg);
            animation-fill-mode: forwards;
            opacity: 0;
            content: '';
            width: 36px;
            height: 70px;
            border-right: 10px solid #19875460;
            border-top: 10px solid #19875460;
            border-radius: 2px;
            position: absolute;
            left: 30px;
            top: 5px;
        }

    @@keyframes checkmark {
        0%

    {
        opacity: 0;
        height: 0;
        width: 0;
    }

    40% {
        opacity: 1;
        height: 70px;
        width: 10px;
    }

    70% {
        opacity: 1;
        height: 70px;
        width: 36px;
    }

    100% {
        opacity: 1;
        border-right: 10px solid #198754;
        border-top: 10px solid #198754;
    }

    }
</style>