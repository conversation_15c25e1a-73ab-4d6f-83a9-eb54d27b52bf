﻿
using System.Net.Mail;
using System.Net;
using Microsoft.Extensions.Options;
using Core.Helper;
using Core.Service.Contract;

namespace Services;

public class EmailService : IEmailService
{
    private readonly EmailSettings _options;

    public EmailService(IOptions<EmailSettings> options)
    {
        _options = options.Value;
    }

    public async Task SendPasswordResetEmailAsync(string toEmail, string resetLink)
    {
        var subject = "Password Reset Request";
        var body = $@"
            <h2>Password Reset</h2>
            <p>Please click the link below to reset your password:</p>
            <p><a href='{resetLink}'>Reset Password</a></p>
            <p>If you didn't request this, please ignore this email.</p>
            <p>This link will expire in 24 hours.</p>";

        await SendEmailAsync(toEmail, subject, body);
    }

    public async Task SendEmailAsync(string toEmail, string subject, string body)
    {
        try
        {
          

            using var message = new MailMessage(_options.SenderEmail , toEmail)
            {
                
                Subject = subject,
                Body = body,
                IsBodyHtml = true
            };

            using var client = new SmtpClient(_options.Server, _options.Port)
            {
                EnableSsl = true,
                Credentials = new NetworkCredential(_options.SenderEmail, _options.Password)
            };

            await client.SendMailAsync(message);
        }
        catch (Exception ex)
        {
            throw new Exception("Failed to send email", ex);
        }
    }
}

