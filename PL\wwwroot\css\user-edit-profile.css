/* RTL specific adjustments */
.me-1, .me-2, .me-3, .me-4 {
    margin-right: 0 !important;
}

.me-1 {
    margin-left: 0.25rem !important;
}

.me-2 {
    margin-left: 0.5rem !important;
}

.me-3 {
    margin-left: 1rem !important;
}

.me-4 {
    margin-left: 1.5rem !important;
}

/* Form styling enhancements */
.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.input-group-text {
    border: 1px solid #ced4da;
}

.input-group:focus-within .input-group-text {
    border-color: #0d6efd;
}

/* Required field indicator styling */
.text-danger {
    font-weight: bold;
}

/* Select2 RTL fixes */
.select2-container--bootstrap-5.select2-container--rtl .select2-selection {
    text-align: right;
}
